<script>
	// import {
	// 	mapMutations
	// } from 'vuex';
	export default {
		globalData: {
			userInfo: null,
			navHeight: 0,
			userUrl: '',
		},
		// methods: {
		// 	...mapMutations(['login'])
		// },
		onLaunch: function() {
			// 获取手机系统信息
			uni.getSystemInfo({
				success: res => {
					//导航高度
					this.globalData.navHeight = res.statusBarHeight + 46;
				},
				fail(err) {
					console.log(err);
				}
			})

			//登录
			// let userInfo = uni.getStorageInfoSync('userInfo') || '';
			// if (userInfo.id) {
			// 	uni.getStorage({
			// 		key: 'userInfo',
			// 		success: (res) => {
			// 			this.login(res.data)
			// 		}
			// 	})
			// }
			// console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	view {
		box-sizing: border-box;
	}

	/* #ifndef APP-NVUE */
	@import '@/assets/css/common.scss';

	@import '@/assets/css/iconfont.wxss'; //搜索图标

	// 设置整个项目的背景色
	page {
		background-color: #f5f5f5;
		box-sizing: border-box;
	}

	/* #endif */
	.example-info {
		font-size: 14px;
		color: #333;
		padding: 10px;
	}

	/* 单选、多选 勾选 */




	/*radio未选中时样式 */
	radio .wx-radio-input {
		width: 16px;
		height: 16px;
		border: 2rpx solid #D9DADD;
		box-sizing: border-box;
		margin-right: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改） */
	radio .wx-radio-input.wx-radio-input-checked {
		width: 16px;
		height: 16px;
		background-color: #2C5DE5;
		border: 2rpx solid #2C5DE5;
	}

	/* 选中后的 对勾样式 （白色对勾 可根据UI需求自己修改） */
	radio .wx-radio-input.wx-radio-input-checked::before {
		/* 去除对号 */
		content: '';
		width: 8px;
		height: 8px;
		border-radius: 50%;
		background-color: #FFFFFF;
	}
</style>
