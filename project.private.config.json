{"libVersion": "2.21.2", "projectname": "work", "condition": {"miniprogram": {"list": [{"name": "登录", "pathName": "pages/login/login", "query": "", "scene": null}]}}, "setting": {"urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false, "useIsolateContext": false}}