<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class='nav bg-white' :style="'height:'+navH+'px'">
      <view class='nav-title'></view>
    </view>

    <!-- 内容主体 -->
    <scroll-view class='bg-gray overflow' :style="'height:calc(100vh - '+navH+'px)'" scroll-y>
      <view class="main-container">
        <!-- 登录标题 -->
        <view class="login-title">
          <view class="login-title-view">登录</view>
          <text class="login-title-text">欢迎使用通用航空运行管理监控系统</text>
        </view>

        <!-- 登录表单 -->
        <view class="login-container">
          <view class="inputNum" v-if="msmLogin">
            <!-- 手机号输入 -->
            <view class="phone">
              <view class="phone-container">
                <text class="phone-container-text">手机号码</text>
                <input
                    class="inputNum-input"
                    type="number"
                    maxlength="11"
                    v-model="phoneNum"
                    :style="phoneInputStyle"
                    :class="phoneInputClass"
                    @input="onPhoneInput"
                    @blur="onPhoneBlur"
                    placeholder="请输入手机号码"
                />
                <text
                    class="iconfont icon-close-circle-fill"
                    v-if="phoneNum.length"
                    @click="clearPhoneValue"
                ></text>
              </view>
              <!-- 手机号错误提示 -->
              <view class="iconfont icon-a-LeftIcon" v-if="showWarning">
                <span class="warning-words">{{ warningWords }}</span>
              </view>
            </view>

            <!-- 验证码输入 -->
            <view class="verification">
              <view class="verify-input-container">
                <text>验证码</text>
                <input
                    class="verification-input"
                    type="number"
                    v-model="verCode"
                    :style="verifyInputStyle"
                    :class="verifyInputClass"
                    @input="onVerifyCodeInput"
                    @blur="onVerifyCodeBlur"
                    placeholder="请输入验证码"
                />
              </view>
              <!-- 验证码错误提示 -->
              <view class="iconfont icon-a-LeftIcon vertifyIcon" v-if="showVerifyWarning">
                <span class="verify-warning-words">{{ verifyWarningWords }}</span>
              </view>
              <!-- 获取验证码按钮 -->
              <button
                  @click="getVerifyCode"
                  :class="verifyButtonClass"
                  :disabled="!canGetVerifyCode"
              >
                <text :class="verifyButtonTextClass">{{ remainStr }}</text>
              </button>
            </view>
          </view>

          <!-- 登录按钮区域 -->
          <view class="login-in">
            <view class="weChat-in">
              <button class="sys_btn login-btn" @click="handleLogin">
                进入首页
              </button>
            </view>
            <view class="skip-login" @click="skipLogin">
              跳过登录
            </view>
          </view>

          <!-- 使用说明 -->
          <view class="usage-note">
            使用范围：体验范围提供给通航公司机组人员使用、对外无开放注册流程。
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 整体背景放在最后 -->
    <view class='bg-main'>
      <image class='background' src="../../static/images/bgPlane.jpg"></image>
    </view>
  </view>
</template>

<script>
import {getVerificationCodeByPhoneNumber, login, authLogin} from '../../api/weChat.js';

const App = getApp();
const checkPhone = (phone) => /^1(3|4|5|6|7|8|9)\d{9}$/.test(phone);

export default {
  data() {
    return {
      navH: 0,
      msmLogin: true,
      // 表单数据
      phoneNum: '', // 手机号
      verCode: '', // 验证码

      // 验证状态
      showWarning: false,
      showVerifyWarning: false,
      warningWords: '',
      verifyWarningWords: '',
      isWarningColor: false,
      isVerifyWarningColor: false,

      // 验证码倒计时
      remainTime: 61,
      remainStr: '获取验证码',
      canGetVerifyCode: true, // 是否可以获取验证码
      isCountingDown: false, // 是否正在倒计时

      // 样式相关
      warningColor: '#D9DADD',
      verifyWarningColor: '#D9DADD',

      // 微信相关（保留）
      wxCode: '',
    }
  },

  computed: {
    // 手机号输入框样式
    phoneInputStyle() {
      const borderColor = this.isWarningColor ? '#E83F4E' : (this.phoneNum.length > 0 ? '#2C5DE5' : '#D9DADD');
      return `border-bottom: 1px solid ${borderColor}`;
    },

    // 手机号输入框类名
    phoneInputClass() {
      return [
        this.phoneNum.length === 0 ? 'active1' : 'active2',
        {'warning-true-color': this.isWarningColor}
      ];
    },

    // 验证码输入框样式
    verifyInputStyle() {
      const borderColor = this.isVerifyWarningColor ? '#E83F4E' : (this.verCode.length > 0 ? '#2C5DE5' : '#D9DADD');
      return `border-bottom: 1px solid ${borderColor}`;
    },

    // 验证码输入框类名
    verifyInputClass() {
      return [
        this.verCode.length === 0 ? 'active1' : 'active2',
        {'verify-warning-color': this.isVerifyWarningColor}
      ];
    },

    // 获取验证码按钮类名
    verifyButtonClass() {
      return [
        'van-btn',
        'not-van-button',
        {'van-btn-color': this.isCountingDown}
      ];
    },

    // 获取验证码按钮文字类名
    verifyButtonTextClass() {
      return [
        'btn-text',
        {'timer-color': this.isCountingDown}
      ];
    }
  },

  onLoad(options) {
    this.navH = Number(App.globalData.navHeight);
    // this.codeLogin()
  },
  methods: {
    //验证手机号格式
    validatePhone(phone) {
      return phone && checkPhone(phone);
    },
    //验证验证码格式
    validateVerifyCode(code) {
      return code;
    },
    //重置手机号验证状态
    resetPhoneValidation() {
      this.showWarning = false;
      this.isWarningColor = false;
      this.warningWords = '';
    },
    //重置验证码验证状态
    resetVerifyValidation() {
      this.showVerifyWarning = false;
      this.isVerifyWarningColor = false;
      this.verifyWarningWords = '';
    },
    //显示手机号错误信息
    showPhoneError(message) {
      this.showWarning = true;
      this.isWarningColor = true;
      this.warningWords = message;
    },
    //显示验证码错误信息
    showVerifyError(message) {
      this.showVerifyWarning = true;
      this.isVerifyWarningColor = true;
      this.verifyWarningWords = message;
    },
//手机号输入事件
    onPhoneInput() {
      this.resetPhoneValidation();
    },
//手机号失去焦点事件
    onPhoneBlur() {
      if (this.phoneNum && !this.validatePhone(this.phoneNum)) {
        this.showPhoneError('请输入正确的手机号码');
      }
    },

//验证码输入事件
    onVerifyCodeInput() {
      this.resetVerifyValidation();
    },
//验证码失去焦点事件
    onVerifyCodeBlur() {
      if (this.verCode && !this.validateVerifyCode(this.verCode)) {
        this.showVerifyError('请输入验证码');
      }
    },
//清空手机号
    clearPhoneValue() {
      this.phoneNum = '';
      this.resetPhoneValidation();
    },
    //获取验证码
    async getVerifyCode() {
      // 检查是否可以获取验证码
      if (!this.canGetVerifyCode) {
        return;
      }

      // 验证手机号
      if (!this.validatePhone(this.phoneNum)) {
        this.showPhoneError('请输入正确的手机号码');
        return;
      }

      // 重置验证状态
      this.resetPhoneValidation();
      this.resetVerifyValidation();

      try {
        // 调用获取验证码接口
        const param = {phoneNumber: this.phoneNum};
        await getVerificationCodeByPhoneNumber(param);

        // 显示成功提示
        uni.showToast({
          title: '验证码已发送',
          icon: 'success',
          duration: 2000
        });

        // 开始倒计时
        this.startCountdown();

      } catch (error) {
        console.error('获取验证码失败:', error);
        uni.showToast({
          title: '验证码发送失败，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    },
    //开始倒计时
    startCountdown() {
      this.canGetVerifyCode = false;
      this.isCountingDown = true;
      this.remainTime = 60;
      this.remainStr = `${this.remainTime}s`;

      const timer = setInterval(() => {
        this.remainTime--;

        if (this.remainTime <= 0) {
          // 倒计时结束
          clearInterval(timer);
          this.resetCountdown();
        } else {
          // 更新倒计时显示
          this.remainStr = `${this.remainTime}s`;
        }
      }, 1000);
    },

    //重置倒计时
    resetCountdown() {
      this.canGetVerifyCode = true;
      this.isCountingDown = false;
      this.remainTime = 60;
      this.remainStr = '获取验证码';
    },
    //处理登录
    async handleLogin() {
      // 验证手机号
      if (!this.validatePhone(this.phoneNum)) {
        this.resetVerifyValidation();
        this.showPhoneError('请输入正确的手机号码');
        return;
      }

      // 验证验证码
      if (!this.validateVerifyCode(this.verCode)) {
        this.resetPhoneValidation();
        this.showVerifyError('请输入验证码');
        return;
      }

      // 检查是否已获取验证码
      if (this.canGetVerifyCode) {
        this.showVerifyError('请先获取验证码');
        return;
      }

      try {
        // 显示加载提示
        uni.showLoading({
          title: '登录中...',
          mask: true
        });

        // 调用登录接口
        const param = {
          loginType: 1,
          phoneNumber: this.phoneNum,
          verificationCode: this.verCode
        };

        const data = await login(param);

        // 存储用户信息
        uni.setStorageSync('token', data.response.data.token);
        uni.setStorageSync('userInfo', data.response.data.userVO);

        // 隐藏加载提示
        uni.hideLoading();

        // 显示成功提示
        uni.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });

        // 跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);

      } catch (error) {
        console.error('登录失败:', error);

        // 隐藏加载提示
        uni.hideLoading();

        // 显示错误提示
        uni.showToast({
          title: error.message || '登录失败，请检查验证码是否正确',
          icon: 'none',
          duration: 2000
        });
      }
    },
    //微信登录
    /*			codeLogin(){
             uni.login({
              provider: 'weixin', //使用微信登录
              success: async function (loginRes) {
                console.log(loginRes)
              let param = {
                code: loginRes.code
              };

              try {
                const data = await authLogin(param);
                console.log(data.response.data.userVO)
                uni.setStorageSync('openId',data.response.data.openId)
                // 将token存到本地存储中
                uni.setStorageSync('token', data.response.data.token);
                //将用户信息存到本地存储
                uni.setStorageSync('userInfo', data.response.data.userVO);
                if(data.response.data.wxMenus!=null && data.response.data.wxMenus.length>0){
                  let colorList =  [  'waiting', 'info', 'search', 'success','warn',  'download']
                  data.response.data.wxMenus.map((i,o)=>{
                    i['menuiconsweb'] = colorList[o]
                  })
                }
                console.log(data.response.data.wxMenus)
                uni.setStorageSync('wxMenus', data.response.data.wxMenus);
                uni.setStorageSync('userRole', data.response.data.userRole || '');
                if(data.response.data.authStatus == 0 || data.response.data.authStatus == 2){
                  uni.showToast({
                    title: '暂无权限，跳转至申请页面！',
                    icon: 'none'
                  })
                  setTimeout(() => {
                    uni.navigateTo({
                      url: '/pages/login/approal'
                    })
                  }, 1000)
                }else{

                  uni.showToast({
                    title: '登录成功',
                    icon: 'none'
                  })
                  setTimeout(() => {
                    uni.switchTab({
                      url: '/pages/home/<USER>'
                    })
                  }, 1000)
                }
              } catch (e) {
                uni.showToast({
                  title: '获取失败，请稍后再试',
                  icon: 'none',
                })
              }
              }
            });
          },*/
    /**
     * 跳过登录
     */
    skipLogin() {
      uni.navigateTo({
        url: '/pages/login/visit'
      });
    },
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100vh;
}

page {
  height: 100%;
}

.background {
  width: 100%;
  height: auto;
  z-index: -1;
  position: absolute;
  top: 0;
  bottom: 0;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 16px;
  left: 0;
  z-index: 10;
  font-family: OPPOSans;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 2px;

  text:first-child {
    position: relative;
    right: 125px;
  }
}

// 输入框状态样式
.active1 {
  border-bottom: 1px solid #D9DADD;
}

.active2 {
  border-bottom: 1px solid #2C5DE5;
}

.warning-true-color {
  border-bottom: 1px solid #E83F4E !important;
}

.verify-warning-color {
  border-bottom: 1px solid #E83F4E !important;
}

.main-container {
  position: absolute;
  bottom: 70px;
  width: 100%;
  height: auto;
  box-sizing: border-box;

  .login-title {
    width: 100%;
    padding-left: 32px;
    font-family: OPPOSans;
    font-style: normal;
    font-weight: normal;
    margin: 4px auto;

    .login-title-view {
      font-size: 24px;
      line-height: 36px;
      color: #2C5DE5;
    }

    .login-title-text {
      font-size: 14px;
      line-height: 20px;
      color: #50545E;
      order: 1;
    }
  }

  .login-container {
    padding: 0 16px;

    .inputNum {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 24px 0;
      background: rgba(255, 255, 255, 0.64);
      border-radius: 8px;
      align-self: stretch;
      flex-grow: 0;
      margin: 32px auto;
      padding: 24px 16px;
      box-sizing: border-box;

      .inputNum-input {
        height: 35px;
        width: 100%;
        // border-bottom: 1px solid #D9DADD;
      }

      .phone {
        width: 100%;
        position: relative;
        margin-bottom: 24px;

        .phone-container {
          width: 100%;
          position: relative;

          .input {
            position: relative;
          }

          .phone-container-text {
            &:last-child {
              position: absolute;
              width: 16px;
              height: 16px;
              bottom: 8px;
              right: 0;
              z-index: 99;
              color: #BEC0C5;
            }
          }

          .icon-close-circle-fill {
            position: absolute;
            right: 0;
            bottom: 6px;
            z-index: 999;
            color: #BEC0C5;
          }
        }

        .icon-a-LeftIcon {
          position: absolute;
          color: #E83F4E;
          display: flex;
          align-items: center;
          margin-top: 4px;

          .warning-words {
            margin-left: 5px;
            font-size: 10px;
            margin: 0px 4px;
          }
        }
      }

      .verification {
        width: 100%;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        position: relative;

        .verify-input-container {
          flex: 1;
          margin-right: 12px;
        }

        .verification-input {
          height: 35px;
          width: 100%;
        }

        .vertifyIcon {
          position: absolute;
          color: #E83F4E;
          display: flex;
          align-items: center;
          bottom: -20px;

          .verify-warning-words {
            margin-left: 5px;
            font-size: 10px;
            margin: 0 4px;
          }
        }

        .van-btn {
          border: 1px solid #2C5DE5;
          box-sizing: border-box;
          border-radius: 4px;
          white-space: nowrap;
          height: 40px;
          line-height: 40px;
          text-align: center;
          margin: 0;
          min-width: 102px;

          .van-btn-text {
            color: #2C5DE5;
            font-size: 14px;
          }
        }

        .van-btn-color {
          border: 1px solid #ABCAFF;

          .btn-text {
            color: #ABCAFF;
          }
        }
      }

    }

    .login-in {
      .weChat-in {
        margin: 12px 0;

        .login-btn {
          width: 100%;
          height: 40px;
          line-height: 40px;
          background-color: #2979FF;
          color: #fff;
          border: none;
          border-radius: 4px;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .skip-login {
        text-align: center;
        color: #333;
        margin: 15px 0;
        font-size: 14px;
        cursor: pointer;
      }
    }

    .usage-note {
      font-size: 13px;
      color: #666;
      line-height: 1.4;
      margin-top: 16px;
    }
  }
}

.not-van-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 16px;
  width: 102px;
  height: 40px;
  border: 1px solid #2C5DE5;
  box-sizing: border-box;
  border-radius: 4px;
  margin: 10px 0;
  background: transparent;

  .btn-text {
    color: #2C5DE5;
    font-weight: 600;
    font-size: 14px;
    margin: 0 8px;
  }

  .timer-color {
    color: #ABCAFF;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}
</style>
