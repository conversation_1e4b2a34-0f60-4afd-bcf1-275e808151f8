// 请求Api
import {
	Request,
	createRequest
} from "@/utils/monkos";
import {
	uniOptions
} from "@/utils/monkos/adapters";
const baseUrl = "https://ga.swcares.com.cn/trade/oc/wechat";
const gpsUrl = "https://ga.swcares.com.cn/trade";//发送gps定位的接口
// const baseUrl = "http://*************:9907";  //服务器地址
// var baseUrl = "http://**************:9907" //本地测试地址陈林
// const baseUrl = "https://ncov.natappvip.cc" //穿透地址


// 扫码
// const baseUrlScan = 'http://**************:4545'
const baseUrlScan = 'https://ga.swcares.com.cn/sale/travel-wechat'





// 错误处理中间件
const parseResponse = async (ctx, next) => {
	if (!(ctx.url.includes('/auth/login'))) {
		uni.showLoading({
			title: '加载中',
			mask: true
		});
	}

	if (uni.getStorageSync('token') && uni.getStorageSync('userInfo')) {
		const tokenValue = uni.getStorageSync('token');
		const idValue = uni.getStorageSync('userInfo').userId;
		const companyCodeValue = uni.getStorageSync('userInfo').companyCode;
		ctx.header.AuthCode = tokenValue;
		ctx.header.AuthID = idValue;
		ctx.header.CompanyCode = companyCodeValue;
		// ctx.header.companyCode = companyCodeValue;
		ctx.header.agentCode = companyCodeValue;
	}
	await next();
	if (ctx.response) {
		uni.hideLoading()
		const {
			code,
			msg,
			flag,
			messages
		} = ctx.response

		if (code == 200 || flag == 1) {
			return;
		}
		if(flag == 0){
			uni.showToast({
				title: messages || 'Bad Request!',
				icon: 'error'
			})
			return;
		}

		if (code !== 200) {
			ctx.error = {
				code: code,
				msg: msg || 'Bad Request!',
			};
			uni.showToast({
				title: msg || 'Bad Request!',
				icon: 'none'
			})
			throw new Error(msg || 'Bad Request!', )
			if (code === 400 && code === 401) {
				uni.showModal({
					title: '请重新登录',
					content: '',
					success: function(res) {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login/login'
							})
							console.log('用户点击确定');
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
				// if (!tokenValue && !idValue) {
				// 	uni.showModal({
				// 		title: '未登录或登录失效，请登录',
				// 		content: '',
				// 		success: function(res) {
				// 			if (res.confirm) {
				// 				uni.reLaunch({
				// 					url: '/pages/login/login'
				// 				})
				// 				console.log('用户点击确定');
				// 			} else if (res.cancel) {
				// 				console.log('用户点击取消');
				// 			}
				// 		}
				// 	});
				// } else {
				if (ctx.response) {
					const {
						code,
						msg
					} = ctx.response
					if (code === 401) {
						uni.reLaunch({
							url: '/pages/login/login'
						})
						return;
					}
					if (code === 500) {
						uni.showToast({
							title: msg,
							icon: 'none'
						});
						return;
					}
					if (code !== 200) {
						ctx.error = {
							code: code,
							msg: msg || 'Bad Request!',
						};
						uni.showToast({
							title: msg || 'Bad Request!',
							icon: 'none'
						})
						throw new Error(msg || 'Bad Request!', )
					}
				}
			}
		};
	}
}

const parseResponse2 = async (ctx, next) => {
	if (!(ctx.url.includes('/auth/login'))) {
		uni.showLoading({
			title: '加载中',
			mask: true
		});
	}

	if (uni.getStorageSync('token') && uni.getStorageSync('userInfo')) {
		const tokenValue = uni.getStorageSync('token');
		const idValue = uni.getStorageSync('userInfo').userId;
		const companyCodeValue = uni.getStorageSync('userInfo').companyCode;
		ctx.header.AuthCode = tokenValue;
		ctx.header.AuthID = idValue;
		ctx.header.CompanyCode = companyCodeValue;
		// ctx.header.companyCode = companyCodeValue;
		ctx.header.agentCode = companyCodeValue;
	}
	await next();
	if (ctx.response) {
		uni.hideLoading()
		const {
			code,
			msg
		} = ctx.response
		if (code !== 200) {
			ctx.error = {
				code: code,
				msg: msg || 'Bad Request!',
			};
			uni.showToast({
				title: msg || 'Bad Request!',
				icon: 'none'
			})
			throw new Error(msg || 'Bad Request!', )
			if (code === 400 && code === 401) {
				uni.showModal({
					title: '请重新登录',
					content: '',
					success: function(res) {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login/login'
							})
							console.log('用户点击确定');
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
				// if (!tokenValue && !idValue) {
				// 	uni.showModal({
				// 		title: '未登录或登录失效，请登录',
				// 		content: '',
				// 		success: function(res) {
				// 			if (res.confirm) {
				// 				uni.reLaunch({
				// 					url: '/pages/login/login'
				// 				})
				// 				console.log('用户点击确定');
				// 			} else if (res.cancel) {
				// 				console.log('用户点击取消');
				// 			}
				// 		}
				// 	});
				// } else {
				if (ctx.response) {
					const {
						code,
						msg
					} = ctx.response
					if (code === 401) {
						uni.reLaunch({
							url: '/pages/login/login'
						})
						return;
					}
					if (code === 500) {
						uni.showToast({
							title: msg,
							icon: 'none'
						});
						return;
					}
					if (code !== 200) {
						ctx.error = {
							code: code,
							msg: msg || 'Bad Request!',
						};
						uni.showToast({
							title: msg || 'Bad Request!',
							icon: 'none'
						})
						throw new Error(msg || 'Bad Request!', )
					}
				}
			}
		};
	}
}

export const api = new Request({
	baseURL: baseUrl,
	...uniOptions
});

export const api2 = new Request({
	baseURL: baseUrlScan,
	...uniOptions
});
export const api_gps = new Request({
	baseURL: gpsUrl,
	...uniOptions
});

api._post = createRequest("POST", api);
api._post2 = createRequest("POST", api2);
api._post_gps = createRequest("POST", api_gps);
api._get = createRequest("GET", api);
api._put = createRequest("PUT", api);
api.baseUrl = baseUrl
api2.baseUrl = baseUrlScan
api_gps.baseUrl = gpsUrl
api.use(parseResponse)
api2.use(parseResponse2)
api_gps.use(parseResponse)

export default api;
